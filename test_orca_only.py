#!/usr/bin/env python3
"""
Test Orca-Only Trading System
Tests the trading system with Jupiter completely removed.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OrcaOnlyTester:
    """Test the Orca-only trading system."""

    def __init__(self):
        """Initialize the tester."""
        self.wallet_address = os.getenv('WALLET_ADDRESS')
        self.helius_api_key = os.getenv('HELIUS_API_KEY')
        
        if not self.wallet_address or not self.helius_api_key:
            raise ValueError("Missing required environment variables")
        
        logger.info(f"🧪 Orca-Only Tester initialized for wallet: {self.wallet_address}")

    async def test_native_swap_builder(self):
        """Test the native swap builder with Orca only."""
        try:
            logger.info("🔨 Testing Native Swap Builder (Orca-only)...")
            
            # Import and initialize native swap builder
            from core.dex.native_swap_builder import NativeSwapBuilder
            from solders.keypair import Keypair
            import base58
            
            # Load keypair
            wallet_private_key = os.getenv('WALLET_PRIVATE_KEY')
            if not wallet_private_key:
                logger.error("❌ No wallet private key found")
                return False
            
            private_key_bytes = base58.b58decode(wallet_private_key)
            keypair = Keypair.from_bytes(private_key_bytes)
            
            # Create native swap builder
            builder = NativeSwapBuilder(self.wallet_address, keypair)
            await builder.initialize()
            
            # Test signal
            test_signal = {
                'action': 'SELL',
                'market': 'SOL-USDC',
                'size': 0.01,  # 0.01 SOL
                'price': 155.0,
                'confidence': 0.8,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"🧪 Testing with signal: {test_signal}")
            
            # Build transaction
            transaction = await builder.build_and_sign_transaction(test_signal)
            
            if transaction:
                logger.info("✅ Native swap builder test PASSED")
                logger.info(f"✅ Transaction type: {type(transaction)}")
                return True
            else:
                logger.error("❌ Native swap builder test FAILED")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing native swap builder: {e}")
            return False

    async def test_unified_transaction_builder(self):
        """Test the unified transaction builder."""
        try:
            logger.info("🔨 Testing Unified Transaction Builder...")
            
            # Import and initialize unified transaction builder
            from core.dex.unified_transaction_builder import UnifiedTransactionBuilder
            from solders.keypair import Keypair
            import base58
            
            # Load keypair
            wallet_private_key = os.getenv('WALLET_PRIVATE_KEY')
            private_key_bytes = base58.b58decode(wallet_private_key)
            keypair = Keypair.from_bytes(private_key_bytes)
            
            # Create unified transaction builder
            builder = UnifiedTransactionBuilder(self.wallet_address, keypair)
            await builder.initialize()
            
            # Test signal
            test_signal = {
                'action': 'BUY',
                'market': 'SOL-USDC',
                'size': 0.01,  # 0.01 SOL
                'price': 155.0,
                'confidence': 0.8,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"🧪 Testing with signal: {test_signal}")
            
            # Build transaction
            transaction = await builder.build_and_sign_transaction(test_signal)
            
            if transaction:
                logger.info("✅ Unified transaction builder test PASSED")
                logger.info(f"✅ Transaction result: {transaction}")
                return True
            else:
                logger.error("❌ Unified transaction builder test FAILED")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing unified transaction builder: {e}")
            return False

    async def test_modern_executor(self):
        """Test the modern transaction executor."""
        try:
            logger.info("⚡ Testing Modern Transaction Executor...")
            
            # Import and initialize modern executor
            from phase_4_deployment.rpc_execution.modern_transaction_executor import ModernTransactionExecutor
            
            # Create modern executor
            executor = ModernTransactionExecutor()
            await executor.initialize()
            
            # Create a simple test transaction (self-transfer)
            from solders.keypair import Keypair
            from solders.system_program import transfer, TransferParams
            from solders.message import MessageV0
            from solders.transaction import VersionedTransaction
            from solders.hash import Hash
            import base58
            import httpx
            
            # Load keypair
            wallet_private_key = os.getenv('WALLET_PRIVATE_KEY')
            private_key_bytes = base58.b58decode(wallet_private_key)
            keypair = Keypair.from_bytes(private_key_bytes)
            
            # Get fresh blockhash
            async with httpx.AsyncClient(timeout=10.0) as client:
                payload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getLatestBlockhash",
                    "params": [{"commitment": "processed"}]
                }
                
                response = await client.post(
                    f"https://mainnet.helius-rpc.com/?api-key={self.helius_api_key}",
                    json=payload
                )
                response.raise_for_status()
                result = response.json()
                
                if "result" not in result:
                    logger.error("❌ Failed to get blockhash")
                    return False
                
                blockhash = result["result"]["value"]["blockhash"]
            
            # Create test transaction (small self-transfer)
            transfer_ix = transfer(
                TransferParams(
                    from_pubkey=keypair.pubkey(),
                    to_pubkey=keypair.pubkey(),
                    lamports=1000  # 0.000001 SOL
                )
            )
            
            # Create message
            message = MessageV0.try_compile(
                payer=keypair.pubkey(),
                instructions=[transfer_ix],
                address_lookup_table_accounts=[],
                recent_blockhash=Hash.from_string(blockhash)
            )
            
            # Create and sign transaction
            versioned_tx = VersionedTransaction(message, [keypair])
            tx_bytes = bytes(versioned_tx)
            
            logger.info("🧪 Testing transaction execution...")
            
            # Execute transaction
            result = await executor.execute_transaction_with_bundles(tx_bytes)
            
            if result and result.get('success'):
                logger.info("✅ Modern executor test PASSED")
                logger.info(f"✅ Execution result: {result}")
                return True
            else:
                logger.error("❌ Modern executor test FAILED")
                logger.error(f"❌ Error: {result.get('error') if result else 'No result'}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing modern executor: {e}")
            return False

    async def run_all_tests(self):
        """Run all tests."""
        logger.info("🚀 Starting Orca-Only System Tests")
        logger.info("="*60)
        
        tests = [
            ("Native Swap Builder", self.test_native_swap_builder),
            ("Unified Transaction Builder", self.test_unified_transaction_builder),
            ("Modern Transaction Executor", self.test_modern_executor)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            logger.info(f"\n🧪 Running {test_name} test...")
            try:
                result = await test_func()
                results[test_name] = result
                if result:
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED")
            except Exception as e:
                logger.error(f"❌ {test_name}: ERROR - {e}")
                results[test_name] = False
        
        # Summary
        logger.info("\n" + "="*60)
        logger.info("🏁 TEST SUMMARY")
        logger.info("="*60)
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"  {test_name}: {status}")
        
        logger.info(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 ALL TESTS PASSED - Orca-only system is working!")
            return True
        else:
            logger.error(f"❌ {total - passed} tests failed")
            return False

async def main():
    """Main function."""
    try:
        tester = OrcaOnlyTester()
        success = await tester.run_all_tests()
        return 0 if success else 1
    except Exception as e:
        logger.error(f"❌ Test runner error: {e}")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
