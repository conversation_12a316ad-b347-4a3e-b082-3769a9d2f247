# RWA Trading System - Comprehensive Requirements
# ===============================================
# 🚨 CRITICAL: Solana ecosystem compatibility matrix
# solders 0.26.0 + solana 0.36.7 = Latest stable combination
# ===============================================

# =============================================================================
# CORE SOLANA ECOSYSTEM (Version-critical dependencies)
# =============================================================================

# Solana core libraries - EXACT versions for compatibility
solders==0.26.0                    # Latest stable - Python bindings for Solana Rust tools
solana==0.36.7                     # Latest stable - Solana Python SDK (uses solders internally)
anchorpy>=0.20.1                   # Anchor framework Python client

# =============================================================================
# CORE SYSTEM DEPENDENCIES
# =============================================================================

# HTTP and networking
httpx>=0.28.0                      # Modern async HTTP client
aiohttp>=3.9.0                     # Async HTTP client/server
websockets>=12.0                   # WebSocket client/server
requests>=2.31.0                   # Synchronous HTTP client

# Configuration and environment
pyyaml>=6.0.1                      # YAML configuration parsing
python-dotenv>=1.0.0               # Environment variable loading
toml>=0.10.2                       # TOML configuration support

# Cryptography and encoding
base58>=2.1.1                      # Base58 encoding/decoding for Solana addresses
cryptography>=41.0.0               # Cryptographic operations
nacl>=1.5.0                        # NaCl cryptography library

# =============================================================================
# DATA PROCESSING AND ANALYSIS
# =============================================================================

# Core data libraries
numpy>=1.24.0,<2.0.0              # Numerical computing (pin major version)
pandas>=2.0.0                      # Data manipulation and analysis
scipy>=1.11.0                      # Scientific computing

# Financial and technical analysis
ta>=0.10.2                         # Technical analysis indicators
vectorbt>=0.25.0                   # Vectorized backtesting (if available)
scikit-learn>=1.3.0                # Machine learning algorithms

# =============================================================================
# VISUALIZATION AND DASHBOARD
# =============================================================================

# Dashboard framework
streamlit>=1.28.0                  # Web dashboard framework
plotly>=5.17.0                     # Interactive plotting
matplotlib>=3.7.0                  # Static plotting
seaborn>=0.12.0                    # Statistical visualization

# =============================================================================
# ASYNC AND CONCURRENCY
# =============================================================================

# Async utilities
asyncio-mqtt>=0.16.0               # MQTT async client
aiofiles>=23.0.0                   # Async file operations
aiosqlite>=0.19.0                  # Async SQLite database operations
tenacity>=8.2.0                    # Retry logic with exponential backoff

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================

# Logging and monitoring
loguru>=0.7.0                      # Advanced logging
prometheus-client>=0.19.0          # Metrics collection
psutil>=5.9.0                      # System monitoring

# Alerting
python-telegram-bot>=20.0          # Telegram bot API

# =============================================================================
# DEVELOPMENT AND TESTING
# =============================================================================

# Testing framework
pytest>=7.4.0                      # Testing framework
pytest-asyncio>=0.21.0             # Async testing support
pytest-cov>=4.1.0                  # Coverage reporting
pytest-mock>=3.11.0                # Mocking utilities

# Code quality
black>=23.0.0                      # Code formatting
isort>=5.12.0                      # Import sorting
flake8>=6.0.0                      # Linting
mypy>=1.5.0                        # Type checking

# =============================================================================
# UTILITIES AND HELPERS
# =============================================================================

# Progress and CLI
tqdm>=4.66.0                       # Progress bars
click>=8.1.0                       # CLI framework
rich>=13.0.0                       # Rich text and beautiful formatting

# Date and time
python-dateutil>=2.8.0             # Date utilities
pytz>=2023.3                       # Timezone handling

# =============================================================================
# OPTIONAL DEPENDENCIES (Install as needed)
# =============================================================================

# Rust integration (for performance-critical components)
# maturin>=1.4.0                   # Rust-Python integration (uncomment if needed)

# Additional API clients (uncomment if using)
# birdeye-sdk>=0.1.0               # Birdeye API client
# helius-sdk>=0.1.0                # Helius API client

# Database support (uncomment if using)
# sqlalchemy>=2.0.0                # SQL toolkit
# redis>=5.0.0                     # Redis client

# =============================================================================
# COMPATIBILITY NOTES
# =============================================================================
#
# 1. solders 0.26.0 is the latest stable version (Feb 2025)
# 2. solana 0.36.7 is the latest stable version (Jun 2025)
# 3. These versions are tested together and compatible
# 4. anchorpy 0.20.1+ works with latest solders/solana
# 5. Python 3.9+ required for all Solana dependencies
# 6. numpy <2.0.0 to avoid breaking changes
#
# UPGRADE PATH:
# - Always upgrade solders and solana together
# - Test thoroughly after any Solana ecosystem upgrades
# - Check anchorpy compatibility when upgrading
# ===============================================
